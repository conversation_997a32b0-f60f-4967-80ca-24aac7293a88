<style>
    .availability-modal .modal-dialog {
        max-width: 1100px;
    }

    .availability-modal .section-card {
        border-radius: 10px;
        border: 1px solid var(--input-border);
        background: var(--white);
        padding: 20px;
        margin-bottom: 20px;
    }

    .availability-modal .section-title {
        color: var(--black);
        font-family: Inter;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--neutral-gray);
    }

    .availability-modal .day-item {
        border-radius: 8px;
        border: 1px solid var(--neutral-gray);
        background: var(--white);
        padding: 15px;
        margin-bottom: 12px;
        transition: all 0.3s ease;
    }

    .availability-modal .day-item:hover {
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    }

    .availability-modal .styled-checkbox {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
    }

    .availability-modal .styled-checkbox input[type="checkbox"] {
        appearance: none;
        width: 18px;
        height: 18px;
        border: 2px solid var(--input-border);
        border-radius: 4px;
        background: var(--white);
        cursor: pointer;
        position: relative;
    }

    .availability-modal .styled-checkbox input[type="checkbox"]:checked {
        background: var(--deep-blue);
        border-color: var(--deep-blue);
    }

    .availability-modal .styled-checkbox input[type="checkbox"]:checked::after {
        content: "\f00c";
        font-family: 'Font Awesome 6 Free';
        font-weight: 900;
        color: var(--white);
        font-size: 10px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .availability-modal .time-input-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .availability-modal .time-input {
        border-radius: 8px;
        border: 1px solid var(--input-border);
        background: var(--white);
        padding: 8px 12px;
        width: 90px;
        font-size: 14px;
        text-align: center;
    }

    .availability-modal .time-input:focus {
        outline: none;
        border-color: var(--deep-blue);
        box-shadow: 0 0 0 2px rgba(2, 12, 135, 0.1);
    }

    .availability-modal .calendar-controls {
        background: var(--whisper-gray);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .availability-modal .btn-nav {
        border: none;
        background: var(--white);
        border-radius: 6px;
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
    }

    .availability-modal .btn-nav:hover {
        background: var(--deep-blue);
        color: var(--white);
    }

    .availability-modal .recurring-options {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .availability-modal .radio-option {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 15px;
        border: 1px solid var(--input-border);
        border-radius: 20px;
        background: var(--white);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .availability-modal .radio-option:hover {
        border-color: var(--deep-blue);
    }

    .availability-modal .radio-option input[type="radio"] {
        appearance: none;
        width: 16px;
        height: 16px;
        border: 2px solid var(--input-border);
        border-radius: 50%;
        background: var(--white);
        cursor: pointer;
        position: relative;
    }

    .availability-modal .radio-option input[type="radio"]:checked {
        border-color: var(--deep-blue);
    }

    .availability-modal .radio-option input[type="radio"]:checked::after {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: var(--deep-blue);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .availability-modal .radio-option:has(input:checked) {
        border-color: var(--deep-blue);
        background: rgba(2, 12, 135, 0.05);
    }



    .availability-modal .vacation-badge {
        background: var(--green);
        color: var(--white);
        padding: 4px 8px;
        border-radius: 15px;
        font-size: 12px;
        margin: 2px;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .availability-modal .btn-primary-custom {
        background: var(--deep-blue);
        border: 1px solid var(--deep-blue);
        color: var(--white);
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .availability-modal .btn-primary-custom:hover {
        background: var(--ocean-blue);
        border-color: var(--ocean-blue);
    }

    .availability-modal .btn-outline-custom {
        background: transparent;
        border: 1px solid var(--input-border);
        color: var(--black);
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .availability-modal .btn-outline-custom:hover {
        border-color: var(--deep-blue);
        color: var(--deep-blue);
    }

    .availability-modal .max-height-300 {
        max-height: 300px;
    }

    .availability-modal .overflow-auto {
        overflow-y: auto;
    }

    .availability-modal .schedule-item {
        border: 1px solid var(--neutral-gray);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
        background: var(--white);
        transition: all 0.3s ease;
    }

    .availability-modal .schedule-item:hover {
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    }

    .availability-modal .day-item input:disabled {
        background: var(--whisper-gray);
        color: var(--gray);
        cursor: not-allowed;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:not(:checked)) .time-input-group {
        display: none !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:checked) .time-input-group {
        display: flex !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:not(:checked)) .closed-text {
        display: block !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:checked) .closed-text {
        display: none !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:not(:checked)) .add-time-slot-container {
        display: none !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:checked) .add-time-slot-container {
        display: block !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:not(:checked)) .additional-time-slots {
        display: none !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:checked) .additional-time-slots {
        display: block !important;
    }

    .availability-modal .add-time-slot-btn {
        background: var(--deep-blue);
        color: var(--white) !important;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
        margin: 8px auto 0;
        transition: all 0.3s ease;
    }

    .availability-modal .add-time-slot-btn i {
        color: var(--white) !important;
    }

    .availability-modal .add-time-slot-btn:hover {
        background: var(--ocean-blue);
        transform: scale(1.1);
    }

    .availability-modal .additional-time-slots {
        margin-top: 10px;
    }

    .availability-modal .additional-time-slot {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 8px;
        padding: 8px;
        background: var(--whisper-gray);
        border-radius: 6px;
    }

    .availability-modal .remove-time-slot-btn {
        background: var(--red);
        color: var(--white);
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 10px;
        transition: all 0.3s ease;
    }

    .availability-modal .remove-time-slot-btn:hover {
        background: #dc2626;
        transform: scale(1.1);
    }

    /* Time picker arrows styling */
    .availability-modal .time-input {
        position: relative;
    }

    .availability-modal .time-input-wrapper {
        position: relative;
        display: inline-block;
    }

    .availability-modal .time-arrows {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        flex-direction: column;
        gap: 2px;
        z-index: 10;
    }

    .availability-modal .time-arrow {
        width: 16px;
        height: 12px;
        background: var(--deep-blue);
        color: white;
        border: none;
        cursor: pointer;
        font-size: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 2px;
        transition: all 0.2s ease;
    }

    .availability-modal .time-arrow:hover {
        background: var(--ocean-blue);
        transform: scale(1.1);
    }

    .availability-modal .time-arrow.up {
        margin-bottom: 1px;
    }

    .availability-modal .time-arrow.down {
        margin-top: 1px;
    }

    /* Time input validation styling */
    .availability-modal .time-input.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .availability-modal .time-error {
        font-size: 11px;
        color: #dc3545;
        margin-top: 4px;
    }

    .availability-modal .time-input {
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .availability-modal .time-input:focus {
        border-color: var(--deep-blue);
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        outline: 0;
    }

    .availability-modal .time-input {
        cursor: pointer;
    }

    .availability-modal .time-input:focus {
        border-color: var(--deep-blue);
        box-shadow: 0 0 0 2px rgba(2, 12, 135, 0.1);
    }

    .availability-modal .flatpickr-input {
        cursor: pointer !important;
    }

    /* Vacation Calendar Modal Specific Styles */
    .vacation-calendar-modal .modal-dialog {
        max-width: 1000px;
    }

    .vacation-calendar-modal .vacation-calendar-container {
        min-height: 400px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
    }

    .vacation-calendar-modal .vacation-preview-container {
        max-height: 300px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid var(--neutral-gray);
        border-radius: 8px;
        background: var(--whisper-gray);
    }

    .vacation-calendar-modal .vacation-preview-container .vacation-badge {
        display: block;
        margin-bottom: 8px;
        padding: 8px 12px;
        background: var(--green);
        color: var(--white);
        border-radius: 6px;
        font-size: 13px;
        position: relative;
    }

    .vacation-calendar-modal .vacation-preview-container .vacation-badge .btn-close {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 10px;
        opacity: 0.8;
    }

    .vacation-calendar-modal .vacation-preview-container .vacation-badge:hover .btn-close {
        opacity: 1;
    }

    /* Flatpickr Calendar Customization */
    .vacation-calendar-modal .flatpickr-calendar {
        width: 100% !important;
        max-width: 100% !important;
        font-size: 14px;
    }

    .vacation-calendar-modal .flatpickr-months {
        padding: 15px;
    }

    .vacation-calendar-modal .flatpickr-month {
        height: auto;
    }

    .vacation-calendar-modal .flatpickr-current-month {
        font-size: 18px;
        font-weight: 600;
        color: var(--deep-blue);
    }

    .vacation-calendar-modal .flatpickr-weekdays {
        background: var(--whisper-gray);
        padding: 10px 0;
    }

    .vacation-calendar-modal .flatpickr-weekday {
        font-weight: 600;
        color: var(--black);
        font-size: 13px;
    }

    .vacation-calendar-modal .flatpickr-days {
        padding: 10px;
    }

    .vacation-calendar-modal .flatpickr-day {
        width: 40px;
        height: 40px;
        line-height: 40px;
        margin: 2px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
    }

    .vacation-calendar-modal .flatpickr-day:hover {
        background: var(--ice-blue);
        border-color: var(--deep-blue);
    }

    .vacation-calendar-modal .flatpickr-day.selected {
        background: var(--deep-blue) !important;
        border-color: var(--deep-blue) !important;
        color: var(--white) !important;
    }

    .vacation-calendar-modal .flatpickr-day.selected:hover {
        background: var(--ocean-blue) !important;
    }



    /* Toastr positioning for modal */
    .toast-container {
        z-index: 999999 !important;
    }



    /* Responsive adjustments */
    @media (max-width: 991px) {
        .availability-modal .modal-dialog {
            max-width: 95%;
            margin: 1rem auto;
        }

        .availability-modal .section-card {
            padding: 15px;
            margin-bottom: 15px;
        }

        .availability-modal .day-item {
            padding: 12px;
        }

        .availability-modal .time-input-group {
            flex-direction: column;
            gap: 8px;
            align-items: stretch;
        }

        .availability-modal .time-input {
            width: 100%;
        }

        .availability-modal .recurring-options {
            flex-direction: column;
            gap: 10px;
        }

        .availability-modal .radio-option {
            justify-content: center;
        }
    }

    @media (max-width: 767px) {
        .availability-modal .modal-dialog {
            margin: 0.5rem;
        }

        .availability-modal .day-item .d-flex {
            flex-direction: column;
            align-items: stretch;
            gap: 10px;
        }

        .availability-modal .calendar-controls {
            padding: 10px;
        }

        .availability-modal .calendar-controls h6 {
            font-size: 12px;
        }
    }
</style>

<div class="modal fade availability-modal" id="availabilityModal" tabindex="-1"
    aria-labelledby="availabilityModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-4">
                <h5 class="modal-title fs-18 semi-bold black" id="availabilityModalLabel">
                    <i class="fas fa-calendar-alt me-2 text-primary"></i>
                    Staff Availability Schedule
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <form>
                    <div class="row">
                        <!-- Left Column: Weekly Schedule -->
                        <div class="col-lg-7">
                            <div class="section-card">
                                <h6 class="section-title">
                                    <i class="fas fa-clock me-2"></i>
                                    Weekly Schedule
                                </h6>

                                <!-- Calendar Navigation -->
                                <div class="calendar-controls d-flex justify-content-between align-items-center">
                                    <button type="button" id="prev-week" class="btn-nav">
                                        <i class="fa-solid fa-chevron-left"></i>
                                    </button>
                                    <h6 id="date-range" class="m-0 fs-14 semi-bold black">Loading...</h6>
                                    <button type="button" id="next-week" class="btn-nav">
                                        <i class="fa-solid fa-chevron-right"></i>
                                    </button>
                                </div>

                                <!-- Days Schedule -->
                                <div class="days-schedule mt-3">
                                    <!-- Monday -->
                                    <div class="day-item" data-day="Monday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="monday-check">
                                                <input type="checkbox" class="day-toggle" id="monday-check">
                                                <span class="fs-14 semi-bold black">Monday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Monday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Tuesday -->
                                    <div class="day-item" data-day="Tuesday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="tuesday-check">
                                                <input type="checkbox" class="day-toggle" id="tuesday-check">
                                                <span class="fs-14 semi-bold black">Tuesday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Tuesday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Wednesday -->
                                    <div class="day-item" data-day="Wednesday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="wednesday-check">
                                                <input type="checkbox" class="day-toggle" id="wednesday-check">
                                                <span class="fs-14 semi-bold black">Wednesday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Wednesday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Thursday -->
                                    <div class="day-item" data-day="Thursday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="thursday-check">
                                                <input type="checkbox" class="day-toggle" id="thursday-check">
                                                <span class="fs-14 semi-bold black">Thursday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Thursday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Friday -->
                                    <div class="day-item" data-day="Friday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="friday-check">
                                                <input type="checkbox" class="day-toggle" id="friday-check">
                                                <span class="fs-14 semi-bold black">Friday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Friday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Saturday -->
                                    <div class="day-item" data-day="Saturday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="saturday-check">
                                                <input type="checkbox" class="day-toggle" id="saturday-check">
                                                <span class="fs-14 semi-bold black">Saturday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Saturday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Sunday -->
                                    <div class="day-item" data-day="Sunday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="sunday-check">
                                                <input type="checkbox" class="day-toggle" id="sunday-check">
                                                <span class="fs-14 semi-bold black">Sunday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Sunday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column: Settings & Options -->
                        <div class="col-lg-5">

                            <!-- Recurring Schedule Settings -->
                            <div class="section-card">
                                <h6 class="section-title">
                                    <i class="fas fa-repeat me-2"></i>
                                    Recurring Schedule
                                </h6>

                                <div class="recurring-options">
                                    <label class="radio-option">
                                        <input type="radio" name="duration" value="4">
                                        <span class="fs-13">4 Weeks</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="duration" value="8">
                                        <span class="fs-13">8 Weeks</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="duration" value="custom">
                                        <span class="fs-13">Custom</span>
                                    </label>
                                </div>

                                <div class="custom-weeks mt-3" id="custom-weeks" style="display: none;">
                                    <label class="form-label form-input-labels">Number of Weeks</label>
                                    <div class="d-flex gap-2">
                                        <input type="number" class="form-control form-inputs-field"
                                            id="custom-weeks-input" placeholder="Enter number of weeks"
                                            min="1" max="52">
                                        <button type="button" class="btn-primary-custom"
                                            id="apply-custom-weeks-btn">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Vacation Days Section -->
                            <div class="section-card">
                                <h6 class="section-title">
                                    <i class="fas fa-umbrella-beach me-2"></i>
                                    Vacation Days
                                </h6>

                                <p class="fs-12 text-muted mb-3">Select dates when you will be unavailable (these dates
                                    will be skipped in the schedule)</p>

                                <div class="mb-3">
                                    <button type="button" class="btn-outline-custom" id="vacation-calendar-btn">
                                        <i class="fas fa-calendar-alt me-1"></i> Select Vacation Dates
                                    </button>
                                </div>

                                <div id="selected-vacations" class="mb-3">
                                    <h6 class="fs-12 mb-2">Selected Vacation Dates:</h6>
                                    <div id="vacation-dates-list" class="d-flex flex-wrap gap-1">
                                        <span class="text-muted fs-12">No vacation dates selected</span>
                                    </div>
                                </div>

                                <input type="hidden" id="vacation-dates" name="vacation_dates">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0 p-4">
                <div class="d-flex justify-content-end gap-3">
                    <button type="button" class="cancel-btn" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="button" class="save-btn" id="save-availability-btn">
                        <i class="fas fa-check me-1"></i> Save Availability
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Vacation Calendar Modal -->
<div class="modal fade availability-modal vacation-calendar-modal" id="vacationCalendarModal" tabindex="-1"
    aria-labelledby="vacationCalendarModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-4">
                <h5 class="modal-title fs-16 semi-bold black" id="vacationCalendarModalLabel">
                    <i class="fas fa-calendar-alt me-2 text-primary"></i>
                    Select Vacation Dates
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="section-card h-100">
                            <h6 class="section-title">
                                <i class="fas fa-calendar me-2"></i>
                                Calendar
                            </h6>
                            <div id="vacation-calendar" class="vacation-calendar-container"></div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="section-card h-100">
                            <h6 class="section-title">
                                <i class="fas fa-list me-2"></i>
                                Selected Dates
                            </h6>
                            <div id="vacation-preview" class="vacation-preview-container">
                                <span class="text-muted fs-12">No dates selected</span>
                            </div>
                            <div class="mt-4">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <small>Click on dates in the calendar to select vacation days. Selected dates will
                                        be excluded from the staff schedule.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0 p-4">
                <div class="d-flex justify-content-end gap-3">
                    <button type="button" class="btn-outline-custom" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="button" class="btn-primary-custom" id="confirm-vacation-dates">
                        <i class="fas fa-check me-1"></i> Confirm Selection
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@push('js')
    <script>
        // Global time validation function
        function addTimeValidation(inputs) {
            $(inputs).each(function() {
                $(this).on('blur', function() {
                    const $dayItem = $(this).closest('.day-item');
                    const isStartTime = $(this).hasClass('start-time') || $(this).hasClass('additional-start-time');
                    const isEndTime = $(this).hasClass('end-time') || $(this).hasClass('additional-end-time');

                    let pairedInput;
                    if (isStartTime) {
                        pairedInput = $(this).parent().find('.end-time, .additional-end-time')[0];
                    } else if (isEndTime) {
                        pairedInput = $(this).parent().find('.start-time, .additional-start-time')[0];
                    }

                    // Validate this pair
                    if (pairedInput) {
                        validateTimeSlot(isStartTime ? this : pairedInput, isEndTime ? this : pairedInput);
                    }
                });
            });
        }

        // Global time validation for a pair of inputs
        function validateTimeSlot(startInput, endInput) {
            if (!startInput || !endInput) return;

            const startTime = startInput.value;
            const endTime = endInput.value;

            if (startTime && endTime) {
                const startMinutes = parseTime(startTime);
                const endMinutes = parseTime(endTime);

                if (startMinutes >= endMinutes) {
                    // End time should be after start time
                    const newEndTime = getNextTimeSlot(startTime, 60);
                    endInput.value = newEndTime;

                    // Show validation message
                    showValidationMessage(endInput, 'End time must be after start time');
                }
            }
        }

        // Helper function to show validation messages
        function showValidationMessage(input, message) {
            // Remove existing message
            $(input).parent().find('.validation-message').remove();

            // Add new message
            const $messageDiv = $('<div>', {
                class: 'validation-message text-danger fs-12 mt-1',
                text: message
            });
            $(input).parent().append($messageDiv);

            // Remove message after 3 seconds
            setTimeout(() => {
                $messageDiv.remove();
            }, 3000);
        }

        // Helper function to parse time string to minutes
        function parseTime(timeStr) {
            if (!timeStr) return null;
            const match = timeStr.match(/(\d{1,2}):(\d{2})\s*(AM|PM)/i);
            if (!match) return null;

            let hours = parseInt(match[1]);
            const minutes = parseInt(match[2]);
            const period = match[3].toUpperCase();

            if (period === 'PM' && hours !== 12) hours += 12;
            if (period === 'AM' && hours === 12) hours = 0;

            return hours * 60 + minutes;
        }

        // Helper function to format minutes to time string
        function formatTime(minutes) {
            const hours = Math.floor(minutes / 60);
            const mins = minutes % 60;
            const period = hours >= 12 ? 'PM' : 'AM';
            const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
            return `${displayHours}:${mins.toString().padStart(2, '0')} ${period}`;
        }

        // Helper function to get next time slot
        function getNextTimeSlot(timeStr, addMinutes = 60) {
            const minutes = parseTime(timeStr);
            if (!minutes && minutes !== 0) return '8:00 PM';
            return formatTime(minutes + addMinutes);
        }

        // Global vacation display function
        function updateVacationDisplay() {
            // Get elements dynamically to ensure they exist
            const $vacationDatesList = $("#vacation-dates-list");
            const $vacationDatesInput = $("#vacation-dates");

            if ($vacationDatesList.length === 0 || $vacationDatesInput.length === 0) {
                console.log('Vacation display elements not found, will update when available');
                return;
            }

            // Update the main vacation display
            if (selectedVacationDates.length === 0) {
                $vacationDatesList.html('<span class="text-muted fs-12">No vacation dates selected</span>');
                $vacationDatesInput.val('');
            } else {
                const dateElements = selectedVacationDates.map(dateStr => {
                    const date = new Date(dateStr);
                    const formattedDate = date.toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric'
                    });
                    return `
                        <span class="vacation-badge me-1 mb-1">
                            ${formattedDate}
                            <button type="button" class="btn-close btn-close-white ms-1" style="font-size: 0.7em;" onclick="removeVacationDate('${dateStr}')"></button>
                        </span>
                    `;
                });
                $vacationDatesList.html(dateElements.join(''));
                $vacationDatesInput.val(selectedVacationDates.join(','));
            }
        }

        // Global vacation dates array (will be initialized properly later)
        let selectedVacationDates = [];

        // Global weekly selections storage
        let weeklySelections = {};

        // Global function to remove vacation date
        function removeVacationDate(dateStr) {
            selectedVacationDates = selectedVacationDates.filter(d => d !== dateStr);
            updateVacationDisplay();
        }

        // Make remove function accessible globally
        window.removeVacationDate = removeVacationDate;

        // Helper function to get week start date
        function getWeekStart(date) {
            const d = new Date(date);
            const day = d.getDay();
            const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
            return new Date(d.setDate(diff));
        }

        $(document).ready(function() {
            let currentStartDate;

            const $dateRangeElement = $("#date-range");
            const $prevWeekButton = $("#prev-week");
            const $nextWeekButton = $("#next-week");

            // Function to get current week's Monday
            function getCurrentWeekMonday() {
                const today = new Date();
                const dayOfWeek = today.getDay();
                const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
                const monday = new Date(today);
                monday.setDate(today.getDate() + daysToMonday);
                // Reset time to midnight for consistent comparison
                monday.setHours(0, 0, 0, 0);
                return monday;
            }

            // Function to normalize date to midnight for comparison
            function normalizeDate(date) {
                const normalized = new Date(date);
                normalized.setHours(0, 0, 0, 0);
                return normalized;
            }

            // Format the date to "dd MMMM, yyyy"
            function formatDate(date) {
                const options = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };
                return date.toLocaleDateString('en-GB', options);
            }

            function updateDateRange() {
                // Debug: Log the current date being used
                console.log('updateDateRange called with currentStartDate:', currentStartDate);

                const startDate = new Date(currentStartDate);
                const endDate = new Date(currentStartDate);
                endDate.setDate(startDate.getDate() + 6);

                const startFormatted = formatDate(startDate);
                const endFormatted = formatDate(endDate);
                $dateRangeElement.text(`${startFormatted} - ${endFormatted}`);

                // Update week info for break functionality
                window.currentWeekStart = new Date(startDate);

                // Disable previous button if we're on current week or earlier
                const currentWeekMonday = getCurrentWeekMonday();
                console.log('Current week Monday:', currentWeekMonday);
                console.log('Displayed week Monday:', currentStartDate);

                const isCurrentWeekOrEarlier = normalizeDate(currentStartDate).getTime() <= currentWeekMonday
                    .getTime();

                if ($prevWeekButton.length) {
                    $prevWeekButton.prop('disabled', isCurrentWeekOrEarlier);
                    if (isCurrentWeekOrEarlier) {
                        $prevWeekButton.css({
                            'opacity': '0.5',
                            'cursor': 'not-allowed'
                        });
                    } else {
                        $prevWeekButton.css({
                            'opacity': '1',
                            'cursor': 'pointer'
                        });
                    }
                }

                // Disable past days if we're on current week
                if (isCurrentWeekOrEarlier) {
                    disablePastDays();
                } else {
                    enableAllDays();
                }

                // Load saved selections for this week
                loadWeekSelections();
            }

            function disablePastDays() {
                const today = new Date();
                const todayDayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

                $(".day-item").each(function() {
                    const $dayElement = $(this);
                    const dayName = $dayElement.attr("data-day");
                    const dayIndex = dayNames.indexOf(dayName);
                    const $checkbox = $dayElement.find(".day-toggle");
                    const $timeInputGroup = $dayElement.find(".time-input-group");
                    const $closedText = $dayElement.find(".closed-text");

                    // If this day has already passed (is before today)
                    if (dayIndex < todayDayOfWeek) {
                        // Disable and uncheck the checkbox
                        if ($checkbox.length) {
                            $checkbox.prop('disabled', true).prop('checked', false);
                        }

                        // Hide time inputs and show closed text
                        if ($timeInputGroup.length) $timeInputGroup.hide();
                        if ($closedText.length) {
                            $closedText.show().text("Past Date").css('color', '#999');
                        }

                        // Disable time inputs
                        const $timeInputs = $dayElement.find(".timePicker");
                        $timeInputs.prop('disabled', true).attr('readonly', 'readonly');

                        // Gray out the entire day item
                        $dayElement.css({
                            'opacity': '0.5',
                            'pointer-events': 'none'
                        });
                    }
                });
            }

            function enableAllDays() {
                $(".day-item").each(function() {
                    const $dayElement = $(this);
                    const $checkbox = $dayElement.find(".day-toggle");
                    const $closedText = $dayElement.find(".closed-text");

                    // Enable checkbox
                    if ($checkbox.length) {
                        $checkbox.prop('disabled', false);
                    }

                    // Reset closed text
                    if ($closedText.length) {
                        $closedText.text("Closed").css('color', '');
                    }

                    // Reset day item styling
                    $dayElement.css({
                        'opacity': '1',
                        'pointer-events': 'auto'
                    });
                });
            }



            // Initialize with current week and display it immediately
            currentStartDate = getCurrentWeekMonday();
            updateDateRange();



            // Add time slot functionality
            function addTimeSlot(dayName) {
                const $dayItem = $(`[data-day="${dayName}"]`);
                if ($dayItem.length === 0) return;

                const $additionalSlotsContainer = $dayItem.find('.additional-time-slots');
                if ($additionalSlotsContainer.length === 0) return;

                // Get the last time slot's end time to set as default start time
                const lastEndTime = getLastEndTime($dayItem[0]);
                const suggestedStartTime = getNextTimeSlot(lastEndTime);
                const suggestedEndTime = getNextTimeSlot(suggestedStartTime, 120); // 2 hours later

                const slotId = Date.now() + Math.random();
                const slotHtml = `
                    <div class="additional-time-slot" data-slot-id="${slotId}">
                        <input class="time-input additional-start-time" type="text" placeholder="e.g. ${suggestedStartTime}" value="${suggestedStartTime}" />
                        <span class="text-muted">to</span>
                        <input class="time-input additional-end-time" type="text" placeholder="e.g. ${suggestedEndTime}" value="${suggestedEndTime}" />
                        <button type="button" class="remove-time-slot-btn" onclick="removeTimeSlot('${dayName}', '${slotId}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                $additionalSlotsContainer.append(slotHtml);

                // Add validation event listeners to new inputs
                const $newSlot = $additionalSlotsContainer.find(`[data-slot-id="${slotId}"]`);
                addTimeValidation($newSlot.find('.time-input'));

                // Save selections after adding time slot
                setTimeout(saveCurrentWeekSelections, 100);
            }

            // Remove time slot functionality
            window.removeTimeSlot = function(dayName, slotId) {
                const $dayItem = $(`[data-day="${dayName}"]`);
                if ($dayItem.length === 0) return;

                const $slotToRemove = $dayItem.find(`[data-slot-id="${slotId}"]`);
                if ($slotToRemove.length) {
                    $slotToRemove.remove();
                    // Save selections after removing time slot
                    setTimeout(saveCurrentWeekSelections, 100);
                }
            };

            function getLastEndTime(dayItem) {
                const $dayItem = $(dayItem);
                // Get main end time
                const mainEndTime = $dayItem.find('.end-time').val() || '7:00 PM';

                // Get all additional slots end times
                const additionalEndTimes = [];
                $dayItem.find('.additional-end-time').each(function() {
                    const value = $(this).val();
                    if (value) additionalEndTimes.push(value);
                });

                // Find the latest time
                let latestTime = mainEndTime;
                let latestMinutes = parseTime(mainEndTime);

                additionalEndTimes.forEach(time => {
                    const minutes = parseTime(time);
                    if (minutes && minutes > latestMinutes) {
                        latestTime = time;
                        latestMinutes = minutes;
                    }
                });

                return latestTime;
            }

            function getNextTimeSlot(timeStr, addMinutes = 60) {
                const minutes = parseTime(timeStr);
                if (!minutes && minutes !== 0) return '8:00 PM';
                return formatTime(minutes + addMinutes);
            }



            // Add event listeners for add time slot buttons
            $(document).on('click', '.add-time-slot-btn', function(e) {
                const dayName = $(this).attr('data-day');
                addTimeSlot(dayName);
            });







            function getWeekKey(date) {
                // Create a unique key for each week (YYYY-MM-DD of Monday)
                return date.toISOString().split('T')[0];
            }

            function saveCurrentWeekSelections() {
                const weekKey = getWeekKey(currentStartDate);
                const selections = {
                    days: {},
                    duration: null,
                    customWeeks: null
                };

                // Save day selections
                $(".day-toggle").each(function() {
                    const $checkbox = $(this);
                    const $parent = $checkbox.closest(".day-item");
                    if ($parent.length) {
                        const dayName = $parent.attr("data-day");
                        const $startTimeInput = $parent.find(".start-time");
                        const $endTimeInput = $parent.find(".end-time");

                        // Get additional time slots
                        const additionalSlots = [];
                        $parent.find('.additional-time-slot').each(function() {
                            const $slot = $(this);
                            const $additionalStart = $slot.find('.additional-start-time');
                            const $additionalEnd = $slot.find('.additional-end-time');
                            const slotId = $slot.attr('data-slot-id');

                            if ($additionalStart.length && $additionalEnd.length) {
                                additionalSlots.push({
                                    id: slotId,
                                    startTime: $additionalStart.val(),
                                    endTime: $additionalEnd.val()
                                });
                            }
                        });

                        selections.days[dayName] = {
                            checked: $checkbox.prop('checked'),
                            startTime: $startTimeInput.length ? $startTimeInput.val() : "10:00 AM",
                            endTime: $endTimeInput.length ? $endTimeInput.val() : "7:00 PM",
                            additionalSlots: additionalSlots
                        };
                    }
                });

                const $selectedDuration = $("input[name='duration']:checked");
                if ($selectedDuration.length) {
                    selections.duration = $selectedDuration.val();
                }

                const $customWeeksInput = $("#custom-weeks-input");
                if ($customWeeksInput.length) {
                    selections.customWeeks = $customWeeksInput.val();
                }

                weeklySelections[weekKey] = selections;
                console.log('Saved selections for week:', weekKey, selections);
            }

            function resetFormToCleanState() {
                // Reset all day checkboxes to unchecked
                $(".day-toggle").each(function() {
                    const $checkbox = $(this);
                    if (!$checkbox.prop('disabled')) { // Don't reset disabled (past) days
                        $checkbox.prop('checked', false);
                        const $parent = $checkbox.closest(".day-item");
                        if ($parent.length) {
                            const $timeInputGroup = $parent.find(".time-input-group");
                            const $closedText = $parent.find(".closed-text");

                            if ($timeInputGroup.length) $timeInputGroup.hide();
                            if ($closedText.length) $closedText.show();

                            const $timeInputs = $parent.find(".timePicker");
                            $timeInputs.attr('readonly', 'readonly').prop('disabled', true);

                            // Clear additional time slots
                            const $additionalSlotsContainer = $parent.find('.additional-time-slots');
                            if ($additionalSlotsContainer.length) {
                                $additionalSlotsContainer.empty();
                            }
                        }
                    }
                });

                // Reset recurring options
                $("input[name='duration']").prop('checked', false);

                const $customWeeks = $("#custom-weeks");
                if ($customWeeks.length) $customWeeks.hide();
            }

            function loadWeekSelections() {
                const weekKey = getWeekKey(currentStartDate);
                const selections = weeklySelections[weekKey];

                console.log('Loading selections for week:', weekKey, selections);

                // First reset to clean state
                resetFormToCleanState();

                // If we have saved selections for this week, restore them
                if (selections && selections.days) {
                    console.log('Restoring selections...');

                    // Restore day selections
                    Object.keys(selections.days).forEach(dayName => {
                        const dayData = selections.days[dayName];
                        const $dayElement = $(`[data-day="${dayName}"]`);

                        if ($dayElement.length && dayData.checked) {
                            console.log('Restoring', dayName, 'as checked');

                            const $checkbox = $dayElement.find(".day-toggle");
                            const $timeInputGroup = $dayElement.find(".time-input-group");
                            const $closedText = $dayElement.find(".closed-text");
                            const $startTimeInput = $dayElement.find(".start-time");
                            const $endTimeInput = $dayElement.find(".end-time");

                            // Only restore if the day is not disabled (not a past day)
                            if ($checkbox.length && !$checkbox.prop('disabled')) {
                                $checkbox.prop('checked', true);

                                if ($timeInputGroup.length) $timeInputGroup.css('display', 'flex');
                                if ($closedText.length) $closedText.hide();

                                if ($startTimeInput.length) $startTimeInput.val(dayData.startTime);
                                if ($endTimeInput.length) $endTimeInput.val(dayData.endTime);

                                const $timeInputs = $dayElement.find(".time-input");
                                addTimeValidation($timeInputs);

                                // Restore additional time slots
                                if (dayData.additionalSlots && dayData.additionalSlots.length > 0) {
                                    const $additionalSlotsContainer = $dayElement.find('.additional-time-slots');
                                    if ($additionalSlotsContainer.length) {
                                        // Clear existing additional slots
                                        $additionalSlotsContainer.empty();

                                        dayData.additionalSlots.forEach(slot => {
                                            const slotHtml = `
                                                <div class="additional-time-slot" data-slot-id="${slot.id}">
                                                    <input class="time-input additional-start-time" value="${slot.startTime}" type="text" placeholder="e.g. 8:00 PM" />
                                                    <span class="text-muted">to</span>
                                                    <input class="time-input additional-end-time" value="${slot.endTime}" type="text" placeholder="e.g. 10:00 PM" />
                                                    <button type="button" class="remove-time-slot-btn" onclick="removeTimeSlot('${dayName}', '${slot.id}')">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            `;
                                            $additionalSlotsContainer.append(slotHtml);
                                        });

                                        // Add validation for restored additional slots
                                        const $restoredInputs = $additionalSlotsContainer.find('.time-input');
                                        addTimeValidation($restoredInputs);
                                    }
                                }
                            }
                        }
                    });

                    // Restore duration selections
                    if (selections.duration) {
                        const $durationRadio = $(`input[name='duration'][value='${selections.duration}']`);
                        if ($durationRadio.length) {
                            $durationRadio.prop('checked', true);

                            if (selections.duration === 'custom') {
                                const $customWeeks = $("#custom-weeks");
                                const $customWeeksInput = $("#custom-weeks-input");
                                if ($customWeeks.length) $customWeeks.show();
                                if ($customWeeksInput.length && selections.customWeeks) {
                                    $customWeeksInput.val(selections.customWeeks);
                                }
                            }
                        }
                    }
                }

                // Clear containers that should reset
                const $schedulePreview = $("#schedule-preview");
                if ($schedulePreview.length) $schedulePreview.hide();
            }



            $prevWeekButton.on("click", function(event) {
                event.preventDefault();

                console.log('Previous button clicked');
                console.log('Current currentStartDate before:', currentStartDate);

                // Don't allow going to previous weeks before current week
                const currentWeekMonday = getCurrentWeekMonday();
                const newDate = new Date(currentStartDate);
                newDate.setDate(newDate.getDate() - 7);

                console.log('Would go to:', newDate);
                console.log('Current week Monday:', currentWeekMonday);

                if (normalizeDate(newDate).getTime() < currentWeekMonday.getTime()) {
                    console.log('Blocked: trying to go before current week, setting to current week');
                    // If trying to go before current week, just set to current week
                    currentStartDate = getCurrentWeekMonday();
                    updateDateRange();
                    return;
                }

                console.log('Allowed: going to previous week');
                saveCurrentWeekSelections(); // Save current selections before changing week
                currentStartDate.setDate(currentStartDate.getDate() - 7);
                console.log('New currentStartDate:', currentStartDate);
                updateDateRange();
            });

            $nextWeekButton.on("click", function(event) {
                event.preventDefault();
                console.log('Next button clicked, saving current selections');
                saveCurrentWeekSelections(); // Save current selections before changing week
                currentStartDate.setDate(currentStartDate.getDate() + 7);
                console.log('Moving to next week:', currentStartDate);
                updateDateRange();
            });


            // Checkbox toggle logic
            $(".day-toggle").each(function() {
                const $checkbox = $(this);
                const $parent = $checkbox.closest(".day-item");
                const day = $parent.attr("data-day");
                const $timeInputGroup = $parent.find(".time-input-group");
                const $closedText = $parent.find(".closed-text");

                $checkbox.on("change", function() {
                    if ($(this).prop('checked')) {
                        // Show time inputs, hide closed text
                        if ($timeInputGroup.length) {
                            $timeInputGroup.css('display', 'flex');
                        }
                        if ($closedText.length) {
                            $closedText.hide();
                        }

                        // Enable time inputs and initialize flatpickr
                        const $timeInputs = $parent.find(".timePicker");
                        $timeInputs.each(function() {
                            const $input = $(this);
                            $input.removeAttr('readonly').prop('disabled', false);

                            // Initialize flatpickr if not already done
                            if (!this._flatpickr) {
                                flatpickr(this, {
                                    enableTime: true,
                                    noCalendar: true,
                                    dateFormat: "h:i K",
                                    time_24hr: false,
                                    clickOpens: true,
                                    allowInput: false,
                                    minuteIncrement: 15,
                                    defaultHour: $input.hasClass('start-time') ? 10 : 19,
                                    defaultMinute: 0
                                });
                            }
                        });
                    } else {
                        // Hide time inputs, show closed text
                        if ($timeInputGroup.length) {
                            $timeInputGroup.hide();
                        }
                        if ($closedText.length) {
                            $closedText.show();
                        }

                        // Disable time inputs
                        const $timeInputs = $parent.find(".timePicker");
                        $timeInputs.attr('readonly', 'readonly').prop('disabled', true);
                    }
                });
            });



            // Initialize the modal with current week immediately
            updateDateRange();

            // Ensure current week is shown when modal opens
            const $availabilityModal = $('#availabilityModal');
            if ($availabilityModal.length) {
                $availabilityModal.on('show.bs.modal', function() {
                    // Always reset to current week when modal opens
                    currentStartDate = getCurrentWeekMonday();
                    updateDateRange();

                    // If in edit mode, populate the modal with existing data
                    if (isEditMode && editAvailabilityData.length > 0) {
                        populateModalWithEditData();
                    }

                    // Update vacation display when modal opens
                    setTimeout(() => {
                        updateVacationDisplay();
                    }, 200);
                });
            }

            // Add event listeners to save selections when form elements change
            $(document).on('change', '.day-toggle', function() {
                console.log('Day toggle changed, saving selections');
                setTimeout(saveCurrentWeekSelections, 100); // Small delay to ensure DOM is updated
            });

            $(document).on('change', 'input[name="duration"]', function() {
                console.log('Duration changed:', $(this).val());
                handleDurationChange($(this).val());
            });

            $(document).on('change', '#custom-weeks-input', function() {
                console.log('Custom weeks input changed');
                setTimeout(saveCurrentWeekSelections, 100);
            });

            // Also save when time inputs change
            $(document).on('input', '.timePicker', function() {
                console.log('Time input changed, saving selections');
                setTimeout(saveCurrentWeekSelections, 100);
            });

            // Add Apply button event listener for custom weeks
            const $applyCustomWeeksBtn = $("#apply-custom-weeks-btn");
            if ($applyCustomWeeksBtn.length) {
                $applyCustomWeeksBtn.on('click', function() {
                    const $customRadio = $('input[name="duration"][value="custom"]');
                    const $customWeeksInput = $("#custom-weeks-input");

                    if ($customRadio.length && $customRadio.prop('checked') && $customWeeksInput.length && $customWeeksInput.val()) {
                        console.log('Apply button clicked, triggering duration change');
                        handleDurationChange('custom');
                    } else if (!$customWeeksInput.val()) {
                        toastr.warning('Please enter the number of weeks first.', 'Missing Input');
                    }
                });
            }

            // Handle duration selection and copy schedule to multiple weeks
            function handleDurationChange(selectedValue) {
                // Show/hide custom weeks input
                const $customWeeks = $("#custom-weeks");
                if (selectedValue === 'custom') {
                    $customWeeks.show();
                } else {
                    $customWeeks.hide();
                }

                // Get current week's selections
                const currentWeekSelections = getCurrentWeekSelections();

                // Check if user has selected any days
                const hasSelections = Object.values(currentWeekSelections.days).some(day => day.checked);

                if (!hasSelections) {
                    toastr.warning('Please select at least one working day first, then choose the duration.',
                        'No Schedule Selected');

                    // Uncheck the radio button
                    $(`input[name="duration"][value="${selectedValue}"]`).prop('checked', false);
                    return;
                }

                // Determine number of weeks to copy
                let weeksCount;
                if (selectedValue === 'custom') {
                    const $customInput = $("#custom-weeks-input");
                    weeksCount = parseInt($customInput.val());
                    if (!weeksCount || weeksCount < 1) {
                        toastr.warning('Please enter a valid number of weeks.', 'Invalid Duration');
                        return;
                    }
                } else {
                    weeksCount = parseInt(selectedValue);
                }

                // Copy current week's schedule to the next weeks
                copyScheduleToMultipleWeeks(currentWeekSelections, weeksCount);

                // Show success message
                toastr.success(`Your schedule has been copied to the next ${weeksCount} weeks.`, 'Schedule Copied');
            }

            function getCurrentWeekSelections() {
                const selections = {
                    days: {},
                    duration: null,
                    customWeeks: null
                };

                // Get day selections
                $(".day-toggle").each(function() {
                    const $checkbox = $(this);
                    const $parent = $checkbox.closest(".day-item");
                    if ($parent.length) {
                        const dayName = $parent.attr("data-day");
                        const $startTimeInput = $parent.find(".start-time");
                        const $endTimeInput = $parent.find(".end-time");

                        selections.days[dayName] = {
                            checked: $checkbox.prop('checked'),
                            startTime: $startTimeInput.length ? $startTimeInput.val() : "10:00 AM",
                            endTime: $endTimeInput.length ? $endTimeInput.val() : "7:00 PM"
                        };
                    }
                });

                // Get duration selection
                const $selectedDuration = $("input[name='duration']:checked");
                if ($selectedDuration.length) {
                    selections.duration = $selectedDuration.val();
                }

                const $customWeeksInput = $("#custom-weeks-input");
                if ($customWeeksInput.length) {
                    selections.customWeeks = $customWeeksInput.val();
                }

                return selections;
            }

            function copyScheduleToMultipleWeeks(baseSelections, weeksCount) {
                const currentWeekMonday = getCurrentWeekMonday();

                // First, save current week selections to capture any unsaved additional slots
                saveCurrentWeekSelections();

                // Get the updated base selections with current form state
                const currentWeekKey = getWeekKey(currentStartDate);
                const updatedBaseSelections = weeklySelections[currentWeekKey] || baseSelections;

                // Copy to each week
                for (let weekOffset = 0; weekOffset < weeksCount; weekOffset++) {
                    const targetWeekMonday = new Date(currentWeekMonday);
                    targetWeekMonday.setDate(currentWeekMonday.getDate() + (weekOffset * 7));

                    const weekKey = getWeekKey(targetWeekMonday);

                    // Deep copy the selections to this week (including additionalSlots arrays)
                    const copiedDays = {};
                    Object.keys(updatedBaseSelections.days).forEach(dayName => {
                        const dayData = updatedBaseSelections.days[dayName];
                        copiedDays[dayName] = {
                            checked: dayData.checked,
                            startTime: dayData.startTime,
                            endTime: dayData.endTime,
                            additionalSlots: dayData.additionalSlots ? [...dayData.additionalSlots.map(
                                slot => ({
                                    id: Date.now() + Math
                                .random(), // Generate new unique ID for each copy
                                    startTime: slot.startTime,
                                    endTime: slot.endTime
                                }))] : []
                        };
                    });

                    weeklySelections[weekKey] = {
                        days: copiedDays,
                        duration: updatedBaseSelections.duration,
                        customWeeks: updatedBaseSelections.customWeeks
                    };

                    console.log(`Copied schedule to week ${weekOffset + 1}:`, weekKey, weeklySelections[weekKey]);
                }
                // Reload the current week to show any changes
                loadWeekSelections();
            }
        });


        $(document).ready(function() {
            // Initialize time validation for all existing time inputs
            function initializeTimeInputs() {
                const $timeInputs = $('.time-input');
                addTimeValidation($timeInputs);
            }
            // Initialize on page load
            initializeTimeInputs();
            // Re-initialize when checkboxes are toggled
            $(document).on('change', '.day-toggle', function() {
                setTimeout(initializeTimeInputs, 100);
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            const $customWeeksInput = $("#custom-weeks");

            $("input[name='duration']").on("change", function() {
                if ($(this).val() === "custom") {
                    $customWeeksInput.show();
                } else {
                    $customWeeksInput.hide();
                }
            });
        });
    </script>

    <script>
        // Global variables (some already declared above)
        let scheduleData = [];
        let vacationCalendar = null;
        let currentWeekStart = new Date();
        let isEditMode = false;
        let editAvailabilityData = [];
        let editVacationsData = [];
        let editRecurringData = {};

        // Global validation functions
        function parseTime(timeStr) {
            if (!timeStr) return null;

            const timeRegex = /^(\d{1,2}):(\d{2})\s*(AM|PM)$/i;
            const match = timeStr.trim().match(timeRegex);

            if (!match) return null;

            let hours = parseInt(match[1]);
            const minutes = parseInt(match[2]);
            const period = match[3].toUpperCase();

            if (hours < 1 || hours > 12 || minutes < 0 || minutes > 59) return null;

            if (period === 'PM' && hours !== 12) hours += 12;
            if (period === 'AM' && hours === 12) hours = 0;

            return hours * 60 + minutes; // Return total minutes
        }

        function formatTime(minutes) {
            const hours = Math.floor(minutes / 60);
            const mins = minutes % 60;
            const period = hours >= 12 ? 'PM' : 'AM';
            const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
            return `${displayHours}:${mins.toString().padStart(2, '0')} ${period}`;
        }

        function showTimeError(input, message) {
            // Remove existing error message
            $(input).parent().find('.time-error').remove();

            // Add new error message
            const $errorDiv = $('<div>', {
                class: 'time-error text-danger fs-11 mt-1',
                text: message
            });
            $(input).parent().append($errorDiv);

            // Remove error after 5 seconds
            setTimeout(() => {
                $errorDiv.remove();
            }, 5000);
        }

        function validateTimeSlot(startInput, endInput) {
            const startTime = $(startInput).val().trim();
            const endTime = $(endInput).val().trim();

            // Clear previous errors
            $(startInput).removeClass('is-invalid');
            $(endInput).removeClass('is-invalid');

            if (!startTime || !endTime) return true; // Allow empty for now

            const startMinutes = parseTime(startTime);
            const endMinutes = parseTime(endTime);

            let hasError = false;

            // Validate format
            if (!startMinutes && startMinutes !== 0) {
                $(startInput).addClass('is-invalid');
                showTimeError(startInput, 'Invalid time format. Use format like "10:00 AM"');
                hasError = true;
            }

            if (!endMinutes && endMinutes !== 0) {
                $(endInput).addClass('is-invalid');
                showTimeError(endInput, 'Invalid time format. Use format like "7:00 PM"');
                hasError = true;
            }

            // Validate end time is after start time
            if (startMinutes !== null && endMinutes !== null && endMinutes <= startMinutes) {
                $(endInput).addClass('is-invalid');
                showTimeError(endInput, 'End time must be after start time');
                hasError = true;
            }

            return !hasError;
        }

        function validateTimeSequence(dayItem) {
            const timeSlots = [];
            const $dayItem = $(dayItem);

            // Get main time slot
            const $mainStart = $dayItem.find('.start-time');
            const $mainEnd = $dayItem.find('.end-time');
            if ($mainStart.val() && $mainEnd.val()) {
                timeSlots.push({
                    start: parseTime($mainStart.val()),
                    end: parseTime($mainEnd.val()),
                    startInput: $mainStart[0],
                    endInput: $mainEnd[0],
                    type: 'main'
                });
            }

            // Get additional time slots
            $dayItem.find('.additional-time-slot').each(function() {
                const $slot = $(this);
                const $startInput = $slot.find('.additional-start-time');
                const $endInput = $slot.find('.additional-end-time');
                if ($startInput.val() && $endInput.val()) {
                    timeSlots.push({
                        start: parseTime($startInput.val()),
                        end: parseTime($endInput.val()),
                        startInput: $startInput[0],
                        endInput: $endInput[0],
                        type: 'additional'
                    });
                }
            });

            // Sort by start time
            timeSlots.sort((a, b) => a.start - b.start);

            // Validate sequence
            let hasError = false;
            for (let i = 1; i < timeSlots.length; i++) {
                const prevSlot = timeSlots[i - 1];
                const currentSlot = timeSlots[i];

                if (currentSlot.start < prevSlot.end) {
                    $(currentSlot.startInput).addClass('is-invalid');
                    showTimeError(currentSlot.startInput,
                        'Time slots cannot overlap. Start time must be after previous end time.');
                    hasError = true;
                }
            }
            return !hasError;
        }

        function validateAllTimeInputs() {
            let hasErrors = false;
            const errorMessages = [];
            // Check all checked days for time validation
            $('.day-item').each(function() {
                const $dayItem = $(this);
                const $checkbox = $dayItem.find('.day-toggle');
                if ($checkbox.length && $checkbox.prop('checked')) {
                    const dayName = $dayItem.attr('data-day');
                    // Validate main time slot
                    const $startInput = $dayItem.find('.start-time');
                    const $endInput = $dayItem.find('.end-time');
                    if (!$startInput.val().trim() || !$endInput.val().trim()) {
                        errorMessages.push(`${dayName}: Please enter both start and end times.`);
                        hasErrors = true;
                    } else {
                        // Validate format and sequence
                        if (!validateTimeSlot($startInput[0], $endInput[0])) {
                            hasErrors = true;
                        }
                    }
                    // Validate additional time slots
                    $dayItem.find('.additional-time-slot').each(function() {
                        const $slot = $(this);
                        const $additionalStart = $slot.find('.additional-start-time');
                        const $additionalEnd = $slot.find('.additional-end-time');
                        if (!$additionalStart.val().trim() || !$additionalEnd.val().trim()) {
                            errorMessages.push(
                                `${dayName}: Please enter times for all additional slots or remove empty ones.`
                                );
                            hasErrors = true;
                        } else {
                            if (!validateTimeSlot($additionalStart[0], $additionalEnd[0])) {
                                hasErrors = true;
                            }
                        }
                    });
                    // Validate time sequence for this day
                    if (!validateTimeSequence(this)) {
                        hasErrors = true;
                    }
                }
            });
            if (hasErrors) {
                if (errorMessages.length > 0) {
                    alert("Please fix the following errors:\n\n" + errorMessages.join('\n'));
                } else {
                    alert("Please fix the time validation errors before saving.");
                }
                return false;
            }
            return true;
        }
        // Global functions for edit mode
        window.setEditAvailabilityData = function(availabilityData, vacationsData, recurringData) {
            isEditMode = true;
            editAvailabilityData = availabilityData || [];
            editVacationsData = vacationsData || [];
            editRecurringData = recurringData || {};
            selectedVacationDates = vacationsData || [];
        };
        window.clearEditAvailabilityData = function() {
            isEditMode = false;
            editAvailabilityData = [];
            editVacationsData = [];
            editRecurringData = {};
            selectedVacationDates = [];
        };
        function populateModalWithEditData() {
            console.log('Populating modal with edit data:', editAvailabilityData);
            console.log('Recurring data:', editRecurringData);
            // Clear existing selections first
            $('.day-item').each(function() {
                const $dayItem = $(this);
                const $checkbox = $dayItem.find('.day-toggle');
                const dayName = $dayItem.attr('data-day');

                // Find if this day has availability data (check all entries for this day)
                const dayData = editAvailabilityData.find(item => item.day === dayName);
                console.log(`Looking for ${dayName}, found:`, dayData);
                if (dayData) {
                    // Check the day
                    $checkbox.prop('checked', true);
                    $dayItem.addClass('selected');
                    // Set main time slot
                    if (dayData.main_slot) {
                        const $startInput = $dayItem.find('.start-time');
                        const $endInput = $dayItem.find('.end-time');
                        if ($startInput.length) $startInput.val(dayData.main_slot.start_time);
                        if ($endInput.length) $endInput.val(dayData.main_slot.end_time);
                    }
                    // Add additional time slots
                    if (dayData.additional_slots && dayData.additional_slots.length > 0) {
                        const $additionalSlotsContainer = $dayItem.find('.additional-time-slots');
                        if ($additionalSlotsContainer.length) {
                            $additionalSlotsContainer.empty(); // Clear existing

                            dayData.additional_slots.forEach((slot, index) => {
                                const slotId = Date.now() + index;
                                const slotHtml = `
                                    <div class="additional-time-slot" data-slot-id="${slotId}">
                                        <input class="time-input additional-start-time" type="text" value="${slot.start_time}" />
                                        <span class="text-muted">to</span>
                                        <input class="time-input additional-end-time" type="text" value="${slot.end_time}" />
                                        <button type="button" class="remove-time-slot-btn" onclick="removeTimeSlot('${dayName}', '${slotId}')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                `;
                                $additionalSlotsContainer.append(slotHtml);
                            });
                        }
                    }
                    // Show time inputs and hide closed text
                    const $timeInputsContainer = $dayItem.find('.time-inputs');
                    const $closedText = $dayItem.find('.closed-text');
                    const $addTimeSlotContainer = $dayItem.find('.add-time-slot-container');
                    const $additionalSlotsContainer = $dayItem.find('.additional-time-slots');
                    if ($timeInputsContainer.length) $timeInputsContainer.css('display', 'flex');
                    if ($closedText.length) $closedText.hide();
                    if ($addTimeSlotContainer.length) $addTimeSlotContainer.show();
                    if ($additionalSlotsContainer.length) $additionalSlotsContainer.show();
                } else {
                    // Uncheck the day
                    $checkbox.prop('checked', false);
                    $dayItem.removeClass('selected');
                }
            });
            // Set recurring options if available
            if (editRecurringData && editRecurringData.recurring && editRecurringData.duration) {
                console.log('Setting recurring options:', editRecurringData);
                // Set duration radio button
                const $durationRadio = $(`input[name="duration"][value="${editRecurringData.duration}"]`);
                if ($durationRadio.length) {
                    $durationRadio.prop('checked', true);
                    console.log(`Checked duration radio: ${editRecurringData.duration}`);
                    // If custom, set the custom weeks value and show the input
                    if (editRecurringData.duration === 'custom' && editRecurringData.customWeeks) {
                        const $customWeeksInput = $('#custom-weeks-input');
                        const $customWeeksContainer = $('#custom-weeks');
                        if ($customWeeksInput.length) {
                            $customWeeksInput.val(editRecurringData.customWeeks);
                            console.log(`Set custom weeks: ${editRecurringData.customWeeks}`);
                        }
                        if ($customWeeksContainer.length) {
                            $customWeeksContainer.show();
                        }
                    }
                } else {
                    console.log(`Duration radio not found for value: ${editRecurringData.duration}`);
                }
            }
            // Update vacation display
            if (editVacationsData.length > 0) {
                selectedVacationDates = [...editVacationsData];
                // Use setTimeout to ensure DOM elements are available
                setTimeout(() => {
                    updateVacationDisplay();
                }, 100);
            }
            // Set recurring options if available
            if (editRecurringData && editRecurringData.recurring && editRecurringData.duration) {
                // Set duration radio button
                const $durationRadio = $(`input[name="duration"][value="${editRecurringData.duration}"]`);
                if ($durationRadio.length) {
                    $durationRadio.prop('checked', true);
                    // If custom, set the custom weeks value and show the input
                    if (editRecurringData.duration === 'custom' && editRecurringData.customWeeks) {
                        const $customWeeksInput = $('#custom-weeks-input');
                        const $customWeeksContainer = $('#custom-weeks');
                        if ($customWeeksInput.length) {
                            $customWeeksInput.val(editRecurringData.customWeeks);
                        }
                        if ($customWeeksContainer.length) {
                            $customWeeksContainer.show();
                        }
                    }
                }
            }
        }

        $(document).ready(function() {
            function displayAvailableDates(selectedDays) {
                const weekStart = window.currentWeekStart || new Date();
                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                let html = '';

                selectedDays.forEach(dayName => {
                    const dayIndex = dayNames.indexOf(dayName);
                    const date = new Date(weekStart);
                    date.setDate(weekStart.getDate() + dayIndex);

                    const dateStr = date.toISOString().split('T')[0];
                    const formattedDate = date.toLocaleDateString('en-US', {
                        weekday: 'long',
                        month: 'short',
                        day: 'numeric'
                    });

                    html += `
                        <div class="date-card" data-date="${dateStr}" data-day="${dayName}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${formattedDate}</strong>
                                    <div class="fs-12 text-muted">Click to add breaks</div>
                                </div>
                                <div class="break-count">
                                    <span class="badge bg-secondary" id="break-count-${dateStr}">0 breaks</span>
                                </div>
                            </div>
                            <div class="break-time-inputs" id="breaks-${dateStr}">
                                <button type="button" class="add-break-btn" onclick="addBreakTime('${dateStr}')">
                                    <i class="fas fa-plus me-1"></i> Add Break
                                </button>
                            </div>
                        </div>
                    `;
                });
                availableDatesList.innerHTML = html;
                // Add click handlers for date cards
                $('.date-card').each(function() {
                    $(this).on('click', function() {
                        const $breakInputs = $(this).find('.break-time-inputs');
                        if ($breakInputs.hasClass('show')) {
                            $breakInputs.removeClass('show');
                            $(this).removeClass('selected');
                        } else {
                            // Hide all other break inputs
                            $('.break-time-inputs').removeClass('show');
                            $('.date-card').removeClass('selected');

                            // Show this one
                            $breakInputs.addClass('show');
                            $(this).addClass('selected');
                        }
                    });
                });
            }

            // Generate schedule functionality (for recurring schedules)
            const $generateScheduleBtn = $("#generate-schedule-btn");
            const $schedulePreview = $("#schedule-preview");
            const $scheduleDatesContainer = $("#schedule-dates-container");

            if ($generateScheduleBtn.length) {
                $generateScheduleBtn.on("click", function() {
                    // Save current week selections first
                    saveCurrentWeekSelections();

                    const recurringData = collectRecurringData();
                    const weeksToGenerate = calculateWeeks(recurringData);

                    // For recuring schedules, collect data from all saved weeks
                    const allWeeksData = collectAllWeeksData(weeksToGenerate);
                    if (allWeeksData.length === 0) {
                        toastr.warning('Please select at least one working day first.',
                            'No Working Days Selected');
                        return;
                    }

                    scheduleData = generateRecurringSchedule(allWeeksData, weeksToGenerate);
                    displaySchedulePreview(scheduleData);
                    $schedulePreview.show();
                });
            }

            function collectBasicAvailabilityData() {
                const data = {
                    days: []
                };
                $(".day-item").each(function() {
                    const $dayElement = $(this);
                    const $checkbox = $dayElement.find(".day-toggle");
                    if ($checkbox.length && $checkbox.prop('checked')) {
                        const dayName = $dayElement.attr("data-day");
                        const $startTimeInput = $dayElement.find(".start-time");
                        const $endTimeInput = $dayElement.find(".end-time");

                        const startTime = $startTimeInput.length ? $startTimeInput.val() : "10:00 AM";
                        const endTime = $endTimeInput.length ? $endTimeInput.val() : "7:00 PM";

                        // Collect additional time slots
                        const additionalSlots = [];
                        $dayElement.find('.additional-time-slot').each(function() {
                            const $slot = $(this);
                            const $additionalStart = $slot.find('.additional-start-time');
                            const $additionalEnd = $slot.find('.additional-end-time');

                            if ($additionalStart.length && $additionalEnd.length) {
                                additionalSlots.push({
                                    start_time: $additionalStart.val(),
                                    end_time: $additionalEnd.val()
                                });
                            }
                        });

                        data.days.push({
                            day: dayName,
                            start_time: startTime,
                            end_time: endTime,
                            additional_slots: additionalSlots
                        });
                    }
                });
                return data;
            }

            function collectRecurringData() {
                const $selectedDuration = $("input[name='duration']:checked");
                return {
                    recurring: $selectedDuration.length > 0, // true if any duration is selected
                    duration: $selectedDuration.length ? $selectedDuration.val() : null,
                    customWeeks: $("#custom-weeks-input").length ? $("#custom-weeks-input").val() : null
                };
            }

            function calculateWeeks(recurringData) {
                if (!recurringData.recurring) return 1;

                switch (recurringData.duration) {
                    case '4weeks':
                        return 4;
                    case '8weeks':
                        return 8;
                    case 'custom':
                        return parseInt(recurringData.customWeeks) || 1;
                    default:
                        return 1;
                }
            }

            // Collect data from all saved weeks for recurring schedule generation
            function collectAllWeeksData(weeksToGenerate) {
                const allWeeksData = [];
                const baseWeekStart = new Date(currentStartDate);

                for (let weekOffset = 0; weekOffset < weeksToGenerate; weekOffset++) {
                    const weekStart = new Date(baseWeekStart);
                    weekStart.setDate(baseWeekStart.getDate() + (weekOffset * 7));
                    const weekKey = getWeekKey(weekStart);

                    // Get saved selections for this week
                    const weekSelections = weeklySelections[weekKey];

                    if (weekSelections && weekSelections.days) {
                        Object.keys(weekSelections.days).forEach(dayName => {
                            const dayData = weekSelections.days[dayName];
                            if (dayData.checked) {
                                const weekData = {
                                    week: weekOffset + 1,
                                    weekStart: weekStart,
                                    day: dayName,
                                    start_time: dayData.startTime,
                                    end_time: dayData.endTime,
                                    additional_slots: dayData.additionalSlots || []
                                };
                                allWeeksData.push(weekData);
                            }
                        });
                    }
                }
                return allWeeksData;
            }

            // Generate recurring schedule from all weeks data
            function generateRecurringSchedule(allWeeksData, weeksToGenerate) {
                const schedule = [];
                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

                allWeeksData.forEach(weekData => {
                    const dayIndex = dayNames.indexOf(weekData.day);
                    const currentDate = new Date(weekData.weekStart);
                    currentDate.setDate(weekData.weekStart.getDate() + dayIndex);

                    // Skip vacation dates
                    const dateStr = currentDate.toISOString().split('T')[0];
                    if (!selectedVacationDates.includes(dateStr)) {
                        // Add main time slot
                        schedule.push({
                            date: dateStr,
                            day: weekData.day,
                            start_time: weekData.start_time,
                            end_time: weekData.end_time,
                            week: weekData.week,
                            breaks: [],
                            slot_type: 'main'
                        });

                        // Add additional time slots if they exist
                        if (weekData.additional_slots && weekData.additional_slots.length > 0) {
                            weekData.additional_slots.forEach((slot, index) => {
                                schedule.push({
                                    date: dateStr,
                                    day: weekData.day,
                                    start_time: slot.startTime,
                                    end_time: slot.endTime,
                                    week: weekData.week,
                                    breaks: [],
                                    slot_type: 'additional',
                                    slot_index: index + 1
                                });
                            });
                        }
                    }
                });

                return schedule;
            }

            function generateScheduleDates(days, weeks) {
                const schedule = [];
                const startDate = window.currentWeekStart || new Date();
                // Ensure we start from Monday of the current displayed week
                const baseDate = new Date(startDate);
                const dayOfWeek = baseDate.getDay();
                const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
                baseDate.setDate(baseDate.getDate() + daysToMonday);

                for (let week = 0; week < weeks; week++) {
                    days.forEach(dayData => {
                        const dayOfWeek = getDayOfWeekNumber(dayData.day);
                        const currentDate = new Date(baseDate);
                        currentDate.setDate(baseDate.getDate() + (week * 7) + dayOfWeek);

                        // Skip vacation dates
                        const dateStr = currentDate.toISOString().split('T')[0];
                        if (!selectedVacationDates.includes(dateStr)) {
                            // Add main time slot
                            schedule.push({
                                date: dateStr,
                                day: dayData.day,
                                start_time: dayData.start_time,
                                end_time: dayData.end_time,
                                week: week + 1,
                                breaks: [],
                                slot_type: 'main'
                            });

                            // Add additional time slots if they exist
                            if (dayData.additional_slots && dayData.additional_slots.length > 0) {
                                dayData.additional_slots.forEach((slot, index) => {
                                    schedule.push({
                                        date: dateStr,
                                        day: dayData.day,
                                        start_time: slot.start_time,
                                        end_time: slot.end_time,
                                        week: week + 1,
                                        breaks: [],
                                        slot_type: 'additional',
                                        slot_index: index + 1
                                    });
                                });
                            }
                        }
                    });
                }
                return schedule;
            }

            function getDayOfWeekNumber(dayName) {
                const days = {
                    'Monday': 0,
                    'Tuesday': 1,
                    'Wednesday': 2,
                    'Thursday': 3,
                    'Friday': 4,
                    'Saturday': 5,
                    'Sunday': 6
                };
                return days[dayName] || 0;
            }

            function displaySchedulePreview(schedule) {
                let html = '';

                // Group by week, then by date
                const weekGroups = {};
                schedule.forEach(item => {
                    if (!weekGroups[item.week]) {
                        weekGroups[item.week] = {};
                    }
                    if (!weekGroups[item.week][item.date]) {
                        weekGroups[item.week][item.date] = [];
                    }
                    weekGroups[item.week][item.date].push(item);
                });

                Object.keys(weekGroups).forEach(weekNum => {
                    html += `<div class="week-group mb-4">`;
                    html += `<h6 class="fs-13 semi-bold text-primary mb-3">Week ${weekNum}</h6>`;

                    Object.keys(weekGroups[weekNum]).forEach(date => {
                        const dateItems = weekGroups[weekNum][date];
                        const dateObj = new Date(date);
                        const formattedDate = dateObj.toLocaleDateString('en-US', {
                            weekday: 'long',
                            month: 'short',
                            day: 'numeric'
                        });
                        html += `
                            <div class="schedule-item mb-2 p-3 border rounded" data-date="${date}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>${formattedDate}</strong>
                        `;
                        // Display all time slots for this date
                        dateItems.forEach((item, index) => {
                            const slotLabel = item.slot_type === 'additional' ?
                                ` (Slot ${item.slot_index + 1})` : '';
                            html +=
                                `<div class="fs-12 text-muted">${item.start_time} - ${item.end_time}${slotLabel}</div>`;
                        });
                        html += `
                                    </div>
                                    <div class="text-end">
                                        <div class="badge bg-success fs-10">Working Day</div>
                                        ${dateItems.length > 1 ? `<div class="badge bg-info fs-10 mt-1">${dateItems.length} Time Slots</div>` : ''}
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += `</div>`;
                });
                if (schedule.length === 0) {
                    html =
                        '<div class="text-center text-muted py-4">No schedule generated. Please select working days and recurring options.</div>';
                }
                $scheduleDatesContainer.html(html);
            }
            window.getScheduleData = function() {
                return scheduleData;
            };
        });

        // Vacation calendar functionality
        $(document).ready(function() {
            const $vacationCalendarBtn = $("#vacation-calendar-btn");
            const vacationCalendarModal = new bootstrap.Modal($('#vacationCalendarModal')[0]);
            const $confirmVacationBtn = $("#confirm-vacation-dates");
            const $vacationDatesList = $("#vacation-dates-list");
            const $vacationDatesInput = $("#vacation-dates");

            // Open vacation calendar modal
            $vacationCalendarBtn.on('click', function() {
                vacationCalendarModal.show();
                initializeVacationCalendar();
            });

            function initializeVacationCalendar() {
                // Destroy existing calendar if it exists
                if (vacationCalendar) {
                    vacationCalendar.destroy();
                }

                // Initialize flatpickr calendar
                vacationCalendar = flatpickr("#vacation-calendar", {
                    mode: "multiple",
                    inline: true,
                    dateFormat: "Y-m-d",
                    minDate: "today",
                    defaultDate: selectedVacationDates,
                    onChange: function(selectedDates, dateStr, instance) {
                        updateVacationPreview(selectedDates);
                    }
                });
            }

            function updateVacationPreview(selectedDates) {
                const $vacationPreview = $("#vacation-preview");

                if (selectedDates.length === 0) {
                    $vacationPreview.html('<span class="text-muted fs-12">No dates selected</span>');
                } else {
                    const dateElements = selectedDates.map((date, index) => {
                        const formattedDate = date.toLocaleDateString('en-US', {
                            weekday: 'short',
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric'
                        });
                        return `
                            <div class="vacation-badge">
                                ${formattedDate}
                                <button type="button" class="btn-close btn-close-white" onclick="removeVacationDateFromPreview(${index})"></button>
                            </div>
                        `;
                    });
                    $vacationPreview.html(dateElements.join(''));
                }
            }
            // Function to remove vacation date from preview
            window.removeVacationDateFromPreview = function(index) {
                if (vacationCalendar && vacationCalendar.selectedDates[index]) {
                    const dateToRemove = vacationCalendar.selectedDates[index];
                    const newSelectedDates = vacationCalendar.selectedDates.filter((date, i) => i !== index);
                    vacationCalendar.setDate(newSelectedDates);
                    updateVacationPreview(newSelectedDates);
                }
            };
            // Confirm vacation dates selection
            $confirmVacationBtn.on('click', function() {
                if (vacationCalendar) {
                    selectedVacationDates = vacationCalendar.selectedDates.map(date =>
                        date.toISOString().split('T')[0]
                    );

                    updateVacationDisplay();
                    vacationCalendarModal.hide();
                }
            });
            // Make vacation dates accessible globally
            window.getSelectedVacationDates = function() {
                return selectedVacationDates;
            };
        });
        // Save availability functionality
        $(document).ready(function() {
            const $saveBtn = $("#save-availability-btn");
            $saveBtn.on("click", function() {
                // First validate all time inputs
                if (!validateAllTimeInputs()) {
                    return; // Don't proceed if validation fails
                }
                const availabilityData = collectAvailabilityData();
                if (validateAvailabilityData(availabilityData)) {
                    // Determine if we're in edit mode or create mode
                    const isEditMode = $('#edit-availability-data').length > 0;
                    if (isEditMode) {
                        // Store data in edit modal hidden inputs
                        $('#edit-availability-data').val(JSON.stringify(availabilityData.days));
                        $('#edit-vacations-data').val(JSON.stringify(availabilityData.vacations));
                        $('#edit-recurring-data').val(JSON.stringify({
                            recurring: availabilityData.recurring,
                            duration: availabilityData.duration,
                            customWeeks: availabilityData.customWeeks
                        }));
                        // Update the edit availability button
                        const $editAvailabilityBtn = $('#edit-availability-btn');
                        if ($editAvailabilityBtn.length) {
                            $editAvailabilityBtn.html(
                                'Availability Set <span><i class="fa-solid fa-check text-success"></i></span>'
                            ).addClass('text-success');
                        }
                    } else {
                        // Store data in create modal hidden inputs
                        $('#availability-data').val(JSON.stringify(availabilityData.days));
                        $('#vacations-data').val(JSON.stringify(availabilityData.vacations));
                        $('#recurring-data').val(JSON.stringify({
                            recurring: availabilityData.recurring,
                            duration: availabilityData.duration,
                            customWeeks: availabilityData.customWeeks
                        }));
                        // Update the create availability button
                        const $availabilityBtn = $('#availability-btn');
                        if ($availabilityBtn.length) {
                            $availabilityBtn.html(
                                'Availability Set <span><i class="fa-solid fa-check text-success"></i></span>'
                            ).addClass('text-success');
                        }
                    }
                    // Close the modal
                    const modal = bootstrap.Modal.getInstance($('#availabilityModal')[0]);
                    modal.hide();
                }
            });

            function collectAvailabilityData() {
                const data = {
                    days: [],
                    vacations: [],
                    recurring: false,
                    duration: null,
                    customWeeks: null
                };
                // Collect selected days and times
                $(".day-item").each(function() {
                    const $dayElement = $(this);
                    const $checkbox = $dayElement.find(".day-toggle");
                    if ($checkbox.length && $checkbox.prop('checked')) {
                        const dayName = $dayElement.attr("data-day");
                        const $startTimeInput = $dayElement.find(".start-time");
                        const $endTimeInput = $dayElement.find(".end-time");

                        const startTime = $startTimeInput.length ? $startTimeInput.val() : "10:00 AM";
                        const endTime = $endTimeInput.length ? $endTimeInput.val() : "7:00 PM";

                        // Collect additional time slots
                        const additionalSlots = [];
                        $dayElement.find('.additional-time-slot').each(function() {
                            const $slot = $(this);
                            const $additionalStart = $slot.find('.additional-start-time');
                            const $additionalEnd = $slot.find('.additional-end-time');

                            if ($additionalStart.length && $additionalEnd.length) {
                                additionalSlots.push({
                                    start_time: $additionalStart.val(),
                                    end_time: $additionalEnd.val()
                                });
                            }
                        });
                        data.days.push({
                            day: dayName,
                            start_time: startTime,
                            end_time: endTime,
                            additional_slots: additionalSlots
                        });
                    }
                });
                // Collect vacation dates
                data.vacations = window.getSelectedVacationDates ? window.getSelectedVacationDates() : [];
                // Collect recurring settings
                const $selectedDuration = $("input[name='duration']:checked");
                if ($selectedDuration.length) {
                    data.recurring = true;
                    data.duration = $selectedDuration.val();
                    if (data.duration === 'custom') {
                        data.customWeeks = $("#custom-weeks-input").length ? $("#custom-weeks-input").val() : null;
                    }
                }
                return data;
            }

            function validateAvailabilityData(data) {
                if (data.days.length === 0) {
                    alert("Please select at least one day.");
                    return false;
                }
                if (data.recurring && !data.duration) {
                    alert("Please select a duration for recurring availability.");
                    return false;
                }

                if (data.duration === 'custom' && (!data.customWeeks || data.customWeeks < 1)) {
                    alert("Please enter a valid number of weeks.");
                    return false;
                }
                return true;
            }
        });
    </script>
@endpush
